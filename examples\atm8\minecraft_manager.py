#!/usr/bin/env python3
"""
Minecraft Server Manager
A Python interface to view and manage players on your Minecraft server via RCON.
"""

import subprocess
import json
import os
import re
import time
from datetime import datetime
from typing import List, Dict, Optional
import argparse


class MinecraftServerManager:
    def __init__(self, compose_path: str = "."):
        """Initialize the Minecraft Server Manager."""
        self.compose_path = compose_path
        self.service_name = "mc"

        # Docker environment configuration
        self.rcon_host = os.environ.get('MC_RCON_HOST', 'localhost')
        self.rcon_port = os.environ.get('MC_RCON_PORT', '25575')
        self.rcon_password = os.environ.get('MC_RCON_PASSWORD', 'minecraft')
    
    def _run_rcon_command(self, command: str) -> str:
        """Execute an RCON command and return the result."""
        try:
            cmd = [
                "docker-compose", "-f", f"{self.compose_path}/docker-compose.yml",
                "exec", "-T", self.service_name, "rcon-cli", command
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return f"Error: {result.stderr.strip()}"
        except subprocess.TimeoutExpired:
            return "Error: Command timed out"
        except Exception as e:
            return f"Error: {str(e)}"
    
    def _get_server_logs(self, lines: int = 50) -> str:
        """Get recent server logs."""
        try:
            cmd = [
                "docker-compose", "-f", f"{self.compose_path}/docker-compose.yml",
                "logs", "--tail", str(lines), self.service_name
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.stdout if result.returncode == 0 else result.stderr
        except Exception as e:
            return f"Error getting logs: {str(e)}"
    
    def get_online_players(self) -> Dict:
        """Get list of currently online players."""
        result = self._run_rcon_command("list")
        
        # Parse the result: "There are X of a max of Y players online: player1, player2"
        players_info = {
            "online_count": 0,
            "max_players": 0,
            "players": []
        }
        
        if "There are" in result:
            match = re.search(r"There are (\d+) of a max of (\d+) players online:?(.*)", result)
            if match:
                players_info["online_count"] = int(match.group(1))
                players_info["max_players"] = int(match.group(2))
                if match.group(3).strip():
                    players_info["players"] = [p.strip() for p in match.group(3).split(",") if p.strip()]
        
        return players_info
    
    def get_player_info(self, player_name: str) -> Dict:
        """Get detailed information about a specific player."""
        info = {"name": player_name, "online": False}
        
        # Check if player is online
        online_players = self.get_online_players()
        info["online"] = player_name in online_players["players"]
        
        if info["online"]:
            # Get player position
            pos_result = self._run_rcon_command(f"data get entity {player_name} Pos")
            if "has the following entity data:" in pos_result:
                try:
                    pos_data = pos_result.split(":")[-1].strip()
                    # Parse coordinates like [x, y, z]
                    coords = re.findall(r'-?\d+\.?\d*', pos_data)
                    if len(coords) >= 3:
                        info["position"] = {
                            "x": float(coords[0]),
                            "y": float(coords[1]),
                            "z": float(coords[2])
                        }
                except:
                    pass
            
            # Get player health
            health_result = self._run_rcon_command(f"data get entity {player_name} Health")
            if "has the following entity data:" in health_result:
                try:
                    health_data = health_result.split(":")[-1].strip()
                    health_match = re.search(r'(\d+\.?\d*)', health_data)
                    if health_match:
                        info["health"] = float(health_match.group(1))
                except:
                    pass
            
            # Get player game mode
            gamemode_result = self._run_rcon_command(f"data get entity {player_name} playerGameType")
            if "has the following entity data:" in gamemode_result:
                try:
                    gamemode_data = gamemode_result.split(":")[-1].strip()
                    info["gamemode"] = int(gamemode_data)
                except:
                    pass
        
        return info
    
    def get_server_status(self) -> Dict:
        """Get overall server status."""
        try:
            players = self.get_online_players()

            # Check if we got valid player data (indicates server is running)
            server_running = "There are" in str(players) or players["online_count"] >= 0

            # Get server performance info - try multiple TPS commands
            tps_result = self._get_tps_info()

            # Get additional server info
            server_info = self._get_additional_server_info()

            # Get detailed performance metrics
            performance_metrics = self._get_performance_metrics()

            return {
                "timestamp": datetime.now().isoformat(),
                "players": players,
                "online_players": players,  # For compatibility with frontend
                "tps": tps_result,
                "status": "online" if server_running else "offline",
                "running": server_running,
                **server_info,
                **performance_metrics
            }
        except Exception as e:
            # Return offline status if we can't connect
            return {
                "timestamp": datetime.now().isoformat(),
                "players": {"online_count": 0, "max_players": 0, "players": []},
                "online_players": {"online_count": 0, "max_players": 0, "players": []},
                "tps": f"Connection error: {str(e)}",
                "status": "offline",
                "running": False,
                "error": str(e)
            }

    def _get_tps_info(self) -> str:
        """Get TPS information using various methods."""
        # List of TPS commands to try (in order of preference)
        tps_commands = [
            "forge tps",           # Forge servers
            "tps",                 # Some modded servers
            "/forge tps",          # Alternative forge syntax
            "/tps",                # Alternative syntax
            "spark tps",           # Spark plugin
            "essentials:tps",      # Essentials plugin
            "paper tps",           # Paper servers
            "bukkit:tps",          # Bukkit servers
        ]

        for command in tps_commands:
            try:
                result = self._run_rcon_command(command)

                # Check if the command was successful (not an error)
                if not any(error_phrase in result.lower() for error_phrase in [
                    "error", "unknown", "incomplete", "command not found",
                    "permission", "usage:", "invalid", "syntax error"
                ]):
                    # If we got a valid response, return it
                    if result.strip() and len(result.strip()) > 5:
                        return result.strip()

            except Exception:
                continue

        # Try to get basic server responsiveness as a fallback
        return self._get_server_responsiveness()

    def _get_server_responsiveness(self) -> str:
        """Get comprehensive server performance info."""
        try:
            import time

            # Get Docker container stats for real performance data
            docker_stats = self._get_docker_stats()

            # Test server responsiveness with a simple command
            start_time = time.time()
            result = self._run_rcon_command("list")
            response_time = time.time() - start_time

            # Try to calculate estimated TPS based on response time and server load
            estimated_tps = self._calculate_estimated_tps(response_time, docker_stats)

            # If we have Docker stats, show comprehensive info
            if docker_stats:
                cpu_usage = docker_stats.get('cpu_percent', 0)
                memory_usage = docker_stats.get('memory_usage', 'Unknown')
                memory_percent = docker_stats.get('memory_percent', 0)

                return f"TPS: ~{estimated_tps:.1f} | CPU: {cpu_usage:.1f}% | RAM: {memory_usage} ({memory_percent:.1f}%)"

            # Fallback to response time with estimated TPS
            if "There are" in result and response_time < 5.0:
                return f"TPS: ~{estimated_tps:.1f} | Response: {response_time*1000:.0f}ms"
            else:
                return "Server: Not responding properly"

        except Exception as e:
            return f"Performance monitoring error: {str(e)}"

    def _get_docker_stats(self) -> Dict:
        """Get Docker container performance statistics."""
        try:
            # Get container stats using docker-compose
            cmd = [
                "docker", "stats", "--no-stream", "--format",
                "{{.Container}},{{.CPUPerc}},{{.MemUsage}},{{.MemPerc}}"
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')

                # First try to find by container name pattern
                for line in lines:
                    if any(pattern in line.lower() for pattern in ['atm8-mc', 'mc-1', 'minecraft']):
                        parts = line.split(',')
                        if len(parts) >= 4:
                            cpu_str = parts[1].replace('%', '')
                            mem_usage = parts[2]
                            mem_percent_str = parts[3].replace('%', '')

                            try:
                                cpu_percent = float(cpu_str)
                                mem_percent = float(mem_percent_str)

                                return {
                                    'cpu_percent': cpu_percent,
                                    'memory_usage': mem_usage,
                                    'memory_percent': mem_percent
                                }
                            except ValueError:
                                pass

                # Fallback: find container with highest memory usage (likely Minecraft)
                max_memory = 0
                best_stats = None
                for line in lines:
                    parts = line.split(',')
                    if len(parts) >= 4:
                        try:
                            mem_usage = parts[2]
                            # Extract memory in GiB
                            if 'GiB' in mem_usage:
                                mem_value = float(mem_usage.split('GiB')[0])
                                if mem_value > max_memory:
                                    max_memory = mem_value
                                    cpu_str = parts[1].replace('%', '')
                                    mem_percent_str = parts[3].replace('%', '')
                                    best_stats = {
                                        'cpu_percent': float(cpu_str),
                                        'memory_usage': mem_usage,
                                        'memory_percent': float(mem_percent_str)
                                    }
                        except (ValueError, IndexError):
                            continue

                if best_stats:
                    return best_stats

            return {}
        except Exception:
            return {}

    def _calculate_estimated_tps(self, response_time: float, docker_stats: Dict) -> float:
        """Calculate estimated TPS based on server performance indicators."""
        try:
            # Base TPS calculation on response time
            if response_time < 0.05:  # Very fast response
                base_tps = 20.0
            elif response_time < 0.1:  # Fast response
                base_tps = 19.5
            elif response_time < 0.2:  # Good response
                base_tps = 19.0
            elif response_time < 0.5:  # Fair response
                base_tps = 18.0
            elif response_time < 1.0:  # Slow response
                base_tps = 16.0
            elif response_time < 2.0:  # Very slow response
                base_tps = 12.0
            else:  # Extremely slow
                base_tps = 8.0

            # Adjust based on CPU usage if available
            if docker_stats and 'cpu_percent' in docker_stats:
                cpu_percent = docker_stats['cpu_percent']
                if cpu_percent > 90:
                    base_tps *= 0.7  # Heavy CPU load
                elif cpu_percent > 70:
                    base_tps *= 0.85  # High CPU load
                elif cpu_percent > 50:
                    base_tps *= 0.95  # Moderate CPU load

            # Adjust based on memory usage if available
            if docker_stats and 'memory_percent' in docker_stats:
                mem_percent = docker_stats['memory_percent']
                if mem_percent > 90:
                    base_tps *= 0.8  # High memory usage
                elif mem_percent > 80:
                    base_tps *= 0.9  # Moderate memory usage

            return max(0.1, min(20.0, base_tps))  # Clamp between 0.1 and 20.0

        except Exception:
            return 20.0  # Default to perfect TPS if calculation fails

    def _get_performance_metrics(self) -> Dict:
        """Get detailed performance metrics."""
        metrics = {}

        try:
            # Get Docker stats
            docker_stats = self._get_docker_stats()
            if docker_stats:
                metrics['cpu_usage'] = f"{docker_stats.get('cpu_percent', 0):.1f}%"
                metrics['memory_usage'] = docker_stats.get('memory_usage', 'Unknown')
                metrics['memory_percent'] = f"{docker_stats.get('memory_percent', 0):.1f}%"

            # Get server uptime
            uptime = self._get_server_uptime()
            if uptime:
                metrics['uptime'] = uptime

            # Get world info
            world_info = self._get_world_info()
            if world_info:
                metrics.update(world_info)

        except Exception:
            pass

        return metrics

    def _get_server_uptime(self) -> str:
        """Get server uptime from Docker container."""
        try:
            cmd = ["docker", "ps", "--format", "{{.Status}}", "--filter", "name=mc"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and result.stdout.strip():
                status = result.stdout.strip()
                if "Up" in status:
                    return status.replace("Up ", "")

            return "Unknown"
        except Exception:
            return "Unknown"

    def _get_world_info(self) -> Dict:
        """Get world-specific information."""
        info = {}

        try:
            # Get world seed
            seed_result = self._run_rcon_command("seed")
            if seed_result and "Seed:" in seed_result:
                info['world_seed'] = seed_result.strip()

            # Get world spawn
            spawn_result = self._run_rcon_command("spawnpoint")
            if spawn_result and not any(error in spawn_result.lower() for error in ["error", "unknown"]):
                info['spawn_info'] = spawn_result.strip()

        except Exception:
            pass

        return info

    def _get_additional_server_info(self) -> Dict:
        """Get additional server information."""
        info = {}

        try:
            # Try to get server version
            version_result = self._run_rcon_command("version")
            if version_result and not any(error in version_result.lower() for error in ["error", "unknown"]):
                info["version"] = version_result.strip()
        except:
            pass

        try:
            # Try to get world info
            time_result = self._run_rcon_command("time query daytime")
            if time_result and not any(error in time_result.lower() for error in ["error", "unknown"]):
                info["game_time"] = time_result.strip()
        except:
            pass

        try:
            # Try to get difficulty
            difficulty_result = self._run_rcon_command("difficulty")
            if difficulty_result and not any(error in difficulty_result.lower() for error in ["error", "unknown"]):
                info["difficulty"] = difficulty_result.strip()
        except:
            pass

        return info

    def kick_player(self, player_name: str, reason: str = "Kicked by admin") -> str:
        """Kick a player from the server."""
        return self._run_rcon_command(f"kick {player_name} {reason}")
    
    def ban_player(self, player_name: str, reason: str = "Banned by admin") -> str:
        """Ban a player from the server."""
        return self._run_rcon_command(f"ban {player_name} {reason}")
    
    def unban_player(self, player_name: str) -> str:
        """Unban a player."""
        return self._run_rcon_command(f"pardon {player_name}")
    
    def op_player(self, player_name: str) -> str:
        """Give operator status to a player."""
        return self._run_rcon_command(f"op {player_name}")
    
    def deop_player(self, player_name: str) -> str:
        """Remove operator status from a player."""
        return self._run_rcon_command(f"deop {player_name}")
    
    def teleport_player(self, player_name: str, target: str) -> str:
        """Teleport a player to another player or coordinates."""
        return self._run_rcon_command(f"tp {player_name} {target}")
    
    def send_message(self, message: str) -> str:
        """Send a message to all players."""
        return self._run_rcon_command(f"say {message}")
    
    def send_private_message(self, player_name: str, message: str) -> str:
        """Send a private message to a specific player."""
        return self._run_rcon_command(f"msg {player_name} {message}")
    
    def get_whitelist(self) -> List[str]:
        """Get the whitelist."""
        result = self._run_rcon_command("whitelist list")
        if "There are no whitelisted players" in result:
            return []
        # Parse whitelist result
        if "whitelisted players:" in result:
            players_part = result.split("whitelisted players:")[-1].strip()
            return [p.strip() for p in players_part.split(",") if p.strip()]
        return []
    
    def get_banlist(self) -> List[str]:
        """Get the ban list."""
        result = self._run_rcon_command("banlist players")

        # Handle different possible responses
        if "There are no banned players" in result or "There are 0 ban(s)" in result:
            return []

        # Parse banlist result - format: "There are X ban(s):PlayerName was banned by..."
        if "There are" in result and "ban(s):" in result:
            # Split by "ban(s):" and get the part after it
            ban_part = result.split("ban(s):")[-1].strip()

            # Extract player names - they appear before " was banned by"
            banned_players = []
            # Split by common separators and look for player names
            for line in ban_part.split('\n'):
                if " was banned by" in line:
                    # Extract player name (everything before " was banned by")
                    player_name = line.split(" was banned by")[0].strip()
                    if player_name:
                        banned_players.append(player_name)

            return banned_players

        # Fallback: try the old format for compatibility
        if "banned players:" in result:
            players_part = result.split("banned players:")[-1].strip()
            return [p.strip() for p in players_part.split(",") if p.strip()]

        return []

    def add_to_whitelist(self, player_name: str) -> str:
        """Add a player to the whitelist."""
        return self._run_rcon_command(f"whitelist add {player_name}")

    def remove_from_whitelist(self, player_name: str) -> str:
        """Remove a player from the whitelist."""
        return self._run_rcon_command(f"whitelist remove {player_name}")
    
    def get_recent_activity(self, lines: int = 20) -> List[Dict]:
        """Get recent player activity from logs."""
        logs = self._get_server_logs(lines)
        activities = []
        
        for line in logs.split('\n'):
            # Look for join/leave events
            if 'joined the game' in line:
                match = re.search(r'\[(\d{2}:\d{2}:\d{2})\].*?(\w+) joined the game', line)
                if match:
                    activities.append({
                        "time": match.group(1),
                        "player": match.group(2),
                        "action": "joined"
                    })
            elif 'left the game' in line:
                match = re.search(r'\[(\d{2}:\d{2}:\d{2})\].*?(\w+) left the game', line)
                if match:
                    activities.append({
                        "time": match.group(1),
                        "player": match.group(2),
                        "action": "left"
                    })
        
        return activities[-10:]  # Return last 10 activities


def main():
    """Command line interface for the Minecraft Server Manager."""
    parser = argparse.ArgumentParser(description="Minecraft Server Manager")
    parser.add_argument("--path", default=".", help="Path to docker-compose.yml directory")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Status command
    subparsers.add_parser("status", help="Show server status")
    
    # Players command
    players_parser = subparsers.add_parser("players", help="Show online players")
    players_parser.add_argument("--detailed", action="store_true", help="Show detailed player info")
    
    # Player info command
    info_parser = subparsers.add_parser("info", help="Show detailed player information")
    info_parser.add_argument("player", help="Player name")
    
    # Kick command
    kick_parser = subparsers.add_parser("kick", help="Kick a player")
    kick_parser.add_argument("player", help="Player name")
    kick_parser.add_argument("--reason", default="Kicked by admin", help="Kick reason")
    
    # Ban command
    ban_parser = subparsers.add_parser("ban", help="Ban a player")
    ban_parser.add_argument("player", help="Player name")
    ban_parser.add_argument("--reason", default="Banned by admin", help="Ban reason")
    
    # Unban command
    unban_parser = subparsers.add_parser("unban", help="Unban a player")
    unban_parser.add_argument("player", help="Player name")
    
    # Op command
    op_parser = subparsers.add_parser("op", help="Give operator status")
    op_parser.add_argument("player", help="Player name")
    
    # Deop command
    deop_parser = subparsers.add_parser("deop", help="Remove operator status")
    deop_parser.add_argument("player", help="Player name")
    
    # Message command
    msg_parser = subparsers.add_parser("say", help="Send message to all players")
    msg_parser.add_argument("message", help="Message to send")
    
    # Activity command
    subparsers.add_parser("activity", help="Show recent player activity")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = MinecraftServerManager(args.path)
    
    try:
        if args.command == "status":
            status = manager.get_server_status()
            print(f"Server Status: {status['status']}")
            print(f"Players Online: {status['players']['online_count']}/{status['players']['max_players']}")
            if status['players']['players']:
                print(f"Online Players: {', '.join(status['players']['players'])}")
            print(f"Timestamp: {status['timestamp']}")
        
        elif args.command == "players":
            players = manager.get_online_players()
            print(f"Players Online: {players['online_count']}/{players['max_players']}")
            if players['players']:
                if args.detailed:
                    for player in players['players']:
                        info = manager.get_player_info(player)
                        print(f"\n{player}:")
                        if 'position' in info:
                            pos = info['position']
                            print(f"  Position: {pos['x']:.1f}, {pos['y']:.1f}, {pos['z']:.1f}")
                        if 'health' in info:
                            print(f"  Health: {info['health']}")
                        if 'gamemode' in info:
                            modes = {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}
                            print(f"  Game Mode: {modes.get(info['gamemode'], 'Unknown')}")
                else:
                    print(f"Online: {', '.join(players['players'])}")
            else:
                print("No players online")
        
        elif args.command == "info":
            info = manager.get_player_info(args.player)
            print(f"Player: {info['name']}")
            print(f"Online: {info['online']}")
            if info['online']:
                if 'position' in info:
                    pos = info['position']
                    print(f"Position: {pos['x']:.1f}, {pos['y']:.1f}, {pos['z']:.1f}")
                if 'health' in info:
                    print(f"Health: {info['health']}")
                if 'gamemode' in info:
                    modes = {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}
                    print(f"Game Mode: {modes.get(info['gamemode'], 'Unknown')}")
        
        elif args.command == "kick":
            result = manager.kick_player(args.player, args.reason)
            print(result)
        
        elif args.command == "ban":
            result = manager.ban_player(args.player, args.reason)
            print(result)
        
        elif args.command == "unban":
            result = manager.unban_player(args.player)
            print(result)
        
        elif args.command == "op":
            result = manager.op_player(args.player)
            print(result)
        
        elif args.command == "deop":
            result = manager.deop_player(args.player)
            print(result)
        
        elif args.command == "say":
            result = manager.send_message(args.message)
            print(result)
        
        elif args.command == "activity":
            activities = manager.get_recent_activity()
            if activities:
                print("Recent Activity:")
                for activity in activities:
                    print(f"  {activity['time']} - {activity['player']} {activity['action']}")
            else:
                print("No recent activity found")
    
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
