#!/usr/bin/env python3
"""
Minecraft Server Web Manager
A Flask web interface to view and manage players on your Minecraft server.
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import json
from datetime import datetime
from minecraft_manager import MinecraftServerManager

app = Flask(__name__)
app.secret_key = 'minecraft_server_manager_secret_key'

# Initialize the manager
manager = MinecraftServerManager(".")

@app.route('/')
def index():
    """Main dashboard."""
    try:
        status = manager.get_server_status()
        recent_activity = manager.get_recent_activity()
        return render_template('dashboard.html', 
                             status=status, 
                             recent_activity=recent_activity)
    except Exception as e:
        flash(f"Error getting server status: {e}", "error")
        return render_template('dashboard.html', 
                             status=None, 
                             recent_activity=[])

@app.route('/players')
def players():
    """Players management page."""
    try:
        online_players = manager.get_online_players()
        whitelist = manager.get_whitelist()
        banlist = manager.get_banlist()
        
        # Get detailed info for online players
        detailed_players = []
        for player in online_players['players']:
            info = manager.get_player_info(player)
            detailed_players.append(info)
        
        return render_template('players.html',
                             online_players=online_players,
                             detailed_players=detailed_players,
                             whitelist=whitelist,
                             banlist=banlist)
    except Exception as e:
        flash(f"Error getting player information: {e}", "error")
        return render_template('players.html',
                             online_players={'players': [], 'online_count': 0, 'max_players': 0},
                             detailed_players=[],
                             whitelist=[],
                             banlist=[])

@app.route('/api/status')
def api_status():
    """API endpoint for server status."""
    try:
        status = manager.get_server_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/players')
def api_players():
    """API endpoint for player information."""
    try:
        players = manager.get_online_players()
        return jsonify(players)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/players/detailed')
def api_players_detailed():
    """API endpoint for detailed player information."""
    try:
        online_players = manager.get_online_players()

        # Get detailed info for each online player
        detailed_players = []
        for player in online_players['players']:
            info = manager.get_player_info(player)
            detailed_players.append(info)

        return jsonify({
            "online_count": online_players['online_count'],
            "max_players": online_players['max_players'],
            "players": online_players['players'],
            "detailed_players": detailed_players
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/player/<player_name>')
def api_player_info(player_name):
    """API endpoint for specific player information."""
    try:
        info = manager.get_player_info(player_name)
        return jsonify(info)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/banlist')
def api_banlist():
    """API endpoint for ban list."""
    try:
        banlist = manager.get_banlist()
        return jsonify({"banlist": banlist, "count": len(banlist)})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/whitelist')
def api_whitelist():
    """API endpoint for whitelist."""
    try:
        whitelist = manager.get_whitelist()
        return jsonify({"whitelist": whitelist, "count": len(whitelist)})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/action/kick', methods=['POST'])
def kick_player():
    """Kick a player."""
    player_name = request.form.get('player_name')
    reason = request.form.get('reason', 'Kicked by admin')

    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if not player_name:
        if is_ajax:
            return jsonify({"error": "Player name is required"}), 400
        flash("Player name is required", "error")
        return redirect(url_for('players'))

    try:
        result = manager.kick_player(player_name, reason)

        if is_ajax:
            return jsonify({"success": True, "message": f"Kicked {player_name}: {result}"}), 200

        flash(f"Kicked {player_name}: {result}", "success")
    except Exception as e:
        error_msg = f"Error kicking player: {e}"

        if is_ajax:
            return jsonify({"error": error_msg}), 500

        flash(error_msg, "error")

    return redirect(url_for('players'))

@app.route('/action/ban', methods=['POST'])
def ban_player():
    """Ban a player."""
    player_name = request.form.get('player_name')
    reason = request.form.get('reason', 'Banned by admin')

    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if not player_name:
        if is_ajax:
            return jsonify({"error": "Player name is required"}), 400
        flash("Player name is required", "error")
        return redirect(url_for('players'))

    try:
        result = manager.ban_player(player_name, reason)

        if is_ajax:
            return jsonify({"success": True, "message": f"Banned {player_name}: {result}"}), 200

        flash(f"Banned {player_name}: {result}", "success")
    except Exception as e:
        error_msg = f"Error banning player: {e}"

        if is_ajax:
            return jsonify({"error": error_msg}), 500

        flash(error_msg, "error")

    return redirect(url_for('players'))

@app.route('/action/unban', methods=['POST'])
def unban_player():
    """Unban a player."""
    player_name = request.form.get('player_name')

    # Check if this is an AJAX request
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if not player_name:
        if is_ajax:
            return jsonify({"error": "Player name is required"}), 400
        flash("Player name is required", "error")
        return redirect(url_for('players'))

    try:
        result = manager.unban_player(player_name)

        # Check if the result indicates an error
        if "Error:" in result:
            raise Exception(result)

        if is_ajax:
            return jsonify({"success": True, "message": f"Unbanned {player_name}: {result}"}), 200

        flash(f"Unbanned {player_name}: {result}", "success")
    except Exception as e:
        error_msg = f"Error unbanning player: {e}"

        if is_ajax:
            return jsonify({"error": error_msg}), 500

        flash(error_msg, "error")

    return redirect(url_for('players'))

@app.route('/action/op', methods=['POST'])
def op_player():
    """Give operator status to a player."""
    player_name = request.form.get('player_name')
    
    if not player_name:
        flash("Player name is required", "error")
        return redirect(url_for('players'))
    
    try:
        result = manager.op_player(player_name)
        flash(f"Gave OP to {player_name}: {result}", "success")
    except Exception as e:
        flash(f"Error giving OP: {e}", "error")
    
    return redirect(url_for('players'))

@app.route('/action/deop', methods=['POST'])
def deop_player():
    """Remove operator status from a player."""
    player_name = request.form.get('player_name')
    
    if not player_name:
        flash("Player name is required", "error")
        return redirect(url_for('players'))
    
    try:
        result = manager.deop_player(player_name)
        flash(f"Removed OP from {player_name}: {result}", "success")
    except Exception as e:
        flash(f"Error removing OP: {e}", "error")
    
    return redirect(url_for('players'))

@app.route('/action/teleport', methods=['POST'])
def teleport_player():
    """Teleport a player."""
    player_name = request.form.get('player_name')
    target = request.form.get('target')

    if not player_name or not target:
        flash("Player name and target are required", "error")
        return redirect(url_for('players'))

    try:
        result = manager.teleport_player(player_name, target)
        flash(f"Teleported {player_name} to {target}: {result}", "success")
    except Exception as e:
        flash(f"Error teleporting player: {e}", "error")

    return redirect(url_for('players'))

@app.route('/action/whitelist/add', methods=['POST'])
def add_whitelist():
    """Add a player to the whitelist."""
    player_name = request.form.get('player_name')

    if not player_name:
        flash("Player name is required", "error")
        return redirect(url_for('players'))

    try:
        result = manager.add_to_whitelist(player_name)
        flash(f"Added {player_name} to whitelist: {result}", "success")
    except Exception as e:
        flash(f"Error adding to whitelist: {e}", "error")

    return redirect(url_for('players'))

@app.route('/action/whitelist/remove', methods=['POST'])
def remove_whitelist():
    """Remove a player from the whitelist."""
    player_name = request.form.get('player_name')

    if not player_name:
        flash("Player name is required", "error")
        return redirect(url_for('players'))

    try:
        result = manager.remove_from_whitelist(player_name)
        flash(f"Removed {player_name} from whitelist: {result}", "success")
    except Exception as e:
        flash(f"Error removing from whitelist: {e}", "error")

    return redirect(url_for('players'))

@app.route('/action/message', methods=['POST'])
def send_message():
    """Send a message to all players."""
    message = request.form.get('message')
    
    if not message:
        flash("Message is required", "error")
        return redirect(url_for('index'))
    
    try:
        result = manager.send_message(message)
        flash(f"Message sent: {result}", "success")
    except Exception as e:
        flash(f"Error sending message: {e}", "error")
    
    return redirect(url_for('index'))

@app.route('/console')
def console():
    """Server console page."""
    try:
        logs = manager._get_server_logs(100)
        return render_template('console.html', logs=logs)
    except Exception as e:
        flash(f"Error getting server logs: {e}", "error")
        return render_template('console.html', logs="Error loading logs")

@app.route('/api/console')
def api_console():
    """API endpoint for console logs."""
    try:
        lines = request.args.get('lines', 50, type=int)
        logs = manager._get_server_logs(lines)
        return jsonify({"logs": logs})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    print("Starting Minecraft Server Web Manager...")
    print("Access the web interface at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    app.run(host='0.0.0.0', port=5000, debug=False)
