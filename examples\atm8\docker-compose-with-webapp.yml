version: '3.8'

services:
  # Minecraft Server
  mc:
    image: itzg/minecraft-server:java21
    ports:
      - "25565:25565"
    environment:
      EULA: "TRUE"
      TYPE: "VANILLA"
      VERSION: "1.21.4"
      MEMORY: 4G
      ONLINE_MODE: "FALSE"
      ACCEPT_EULA: "TRUE"
      ENABLE_RCON: "true"
      RCON_PASSWORD: "minecraft"
      RCON_PORT: 25575
    volumes:
      - mc-data:/data
      - ./eula.txt:/data/eula.txt
    networks:
      - minecraft-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mc-health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Web Management Application
  webapp:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - MC_RCON_HOST=mc
      - MC_RCON_PORT=25575
      - MC_RCON_PASSWORD=minecraft
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - .:/app/compose_context:ro
    depends_on:
      mc:
        condition: service_healthy
    networks:
      - minecraft-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # File Browser for server management
  init-filebrowser:
    image: filebrowser/filebrowser
    entrypoint: sh -c
    command:
      - "chown -R 1000: /database"
    restart: no
    volumes:
      - filebrowser-db:/database
    networks:
      - minecraft-network

  filebrowser:
    image: filebrowser/filebrowser
    depends_on:
      init-filebrowser:
        condition: service_completed_successfully
    user: "1000:1000"
    environment:
      FB_DATABASE: /database/filebrowser.db
    volumes:
      - mc-data:/srv
      - filebrowser-db:/database
    ports:
      - "25580:80"
    networks:
      - minecraft-network
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - webapp
    networks:
      - minecraft-network
    restart: unless-stopped
    profiles:
      - with-nginx

volumes:
  mc-data: {}
  filebrowser-db: {}

networks:
  minecraft-network:
    driver: bridge
