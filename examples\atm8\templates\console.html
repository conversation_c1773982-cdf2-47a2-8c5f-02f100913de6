{% extends "base.html" %}

{% block title %}Console - Minecraft Server Manager{% endblock %}

{% block content %}
<!-- Console Header Stats -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-circle text-success fs-4 animate__animated animate__pulse animate__infinite"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%); animation: pulse 2s infinite;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Connection Status</h6>
                        <p class="mb-0 text-white-50">Real-time monitoring</p>
                    </div>
                </div>
                <span class="badge bg-success">
                    <i class="fas fa-wifi me-1"></i>CONNECTED
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-1s">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-sync-alt text-primary fs-4" id="refreshStatusIcon"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(99, 102, 241, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 0.5s;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Auto-Refresh</h6>
                        <p class="mb-0 text-white-50">Every 5 seconds</p>
                    </div>
                </div>
                <span class="badge bg-secondary" id="autoRefreshBadge">
                    <i class="fas fa-pause me-1"></i>DISABLED
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-2s">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-list text-info fs-4"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(6, 182, 212, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 1s;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Log Lines</h6>
                        <p class="mb-0 text-white-50">Current buffer</p>
                    </div>
                </div>
                <span class="badge bg-info">
                    <i class="fas fa-file-alt me-1"></i>100 LINES
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-3s">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-clock text-warning fs-4"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(245, 158, 11, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 1.5s;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Last Updated</h6>
                        <p class="mb-0 text-white-50">Timestamp</p>
                    </div>
                </div>
                <span class="badge bg-warning" id="lastUpdatedBadge">
                    <i class="fas fa-clock me-1"></i>NOW
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main Console -->
<div class="row g-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-4s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal me-2 text-accent-glass"></i>Server Console
                    <span class="badge bg-success ms-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.7rem;">LIVE</span>
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary glass-btn" onclick="refreshLogs()" data-bs-toggle="tooltip" title="Refresh logs">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary glass-btn" onclick="toggleAutoRefresh()" data-bs-toggle="tooltip" title="Toggle auto-refresh">
                        <i class="fas fa-play" id="autoRefreshIcon"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info glass-btn" onclick="clearConsole()" data-bs-toggle="tooltip" title="Clear console">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success glass-btn" onclick="downloadLogs()" data-bs-toggle="tooltip" title="Download logs">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning glass-btn" onclick="toggleFullscreen()" data-bs-toggle="tooltip" title="Toggle fullscreen">
                        <i class="fas fa-expand" id="fullscreenIcon"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <div class="enhanced-console-output" id="consoleOutput">
                    <div class="console-content pt-4">
                        {{ logs if logs else "Loading console logs..." }}
                    </div>
                </div>
                <div class="position-absolute top-0 end-0 p-3" style="z-index: 10;">
                    <div class="d-flex align-items-center glass rounded-pill px-3 py-2 border border-success border-opacity-25">
                        <i class="fas fa-circle text-success me-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.5rem;"></i>
                        <small class="text-white fw-medium">Live Console</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Command Executor -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-5s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-keyboard me-2 text-accent-glass"></i>Command Executor
                    <span class="badge bg-primary ms-2" style="font-size: 0.7rem;">RCON</span>
                </h5>
            </div>
            <div class="card-body">
                <!-- Enhanced Warning Alert -->
                <div class="alert glass border-0 mb-4 animate__animated animate__fadeIn" style="background: rgba(245, 158, 11, 0.1); border: 1px solid rgba(245, 158, 11, 0.3) !important;">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-20 rounded-circle p-3 position-relative">
                                <i class="fas fa-exclamation-triangle text-warning fs-4"></i>
                                <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(245, 158, 11, 0.2) 0%, transparent 70%); animation: pulse 2s infinite;"></div>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="alert-heading mb-1 text-warning">⚠️ Command Execution Warning</h6>
                            <p class="mb-0 text-white-50">Be careful when executing commands. Some commands can affect server performance or player experience.</p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Command Form -->
                <form id="commandForm" class="mb-4">
                    <div class="input-group input-group-lg enhanced-command-input">
                        <span class="input-group-text glass">
                            <i class="fas fa-terminal text-accent-glass"></i>
                        </span>
                        <span class="input-group-text glass border-start-0 text-accent-glass fw-bold">/</span>
                        <input type="text" class="form-control glass-input" id="commandInput" placeholder="Enter command (without /)" required autocomplete="off">
                        <button class="btn btn-primary glass-btn" type="submit">
                            <i class="fas fa-paper-plane me-2"></i>Execute
                        </button>
                    </div>
                    <div class="form-text mt-3 d-flex align-items-center justify-content-between">
                        <div>
                            <i class="fas fa-lightbulb me-1 text-warning"></i>
                            Press <kbd class="glass-kbd">Tab</kbd> for suggestions, <kbd class="glass-kbd">↑</kbd> for history
                        </div>
                        <small class="text-muted-glass">
                            <i class="fas fa-info-circle me-1"></i>
                            Commands are executed via RCON
                        </small>
                    </div>
                </form>

                <!-- Enhanced Command Sections -->
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card glass border-0 animate__animated animate__fadeInLeft">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3 d-flex align-items-center text-white">
                                    <div class="flex-shrink-0">
                                        <div class="bg-warning bg-opacity-20 rounded-circle p-2 position-relative">
                                            <i class="fas fa-star text-warning"></i>
                                        </div>
                                    </div>
                                    <span class="ms-3">Common Commands</span>
                                </h6>
                                <div class="d-flex flex-wrap gap-2">
                                    <button class="btn btn-sm glass-btn btn-outline-primary" onclick="insertCommand('list')" data-bs-toggle="tooltip" title="List online players">
                                        <i class="fas fa-users me-1"></i><code class="text-white">list</code>
                                    </button>
                                    <button class="btn btn-sm glass-btn btn-outline-success" onclick="insertCommand('say Hello everyone!')" data-bs-toggle="tooltip" title="Send message to all players">
                                        <i class="fas fa-comment me-1"></i><code class="text-white">say</code>
                                    </button>
                                    <button class="btn btn-sm glass-btn btn-outline-warning" onclick="insertCommand('time set day')" data-bs-toggle="tooltip" title="Set time to day">
                                        <i class="fas fa-sun me-1"></i><code class="text-white">time set</code>
                                    </button>
                                    <button class="btn btn-sm glass-btn btn-outline-info" onclick="insertCommand('weather clear')" data-bs-toggle="tooltip" title="Clear weather">
                                        <i class="fas fa-cloud-sun me-1"></i><code class="text-white">weather</code>
                                    </button>
                                    <button class="btn btn-sm glass-btn btn-outline-secondary" onclick="insertCommand('gamemode creative')" data-bs-toggle="tooltip" title="Change gamemode">
                                        <i class="fas fa-gamepad me-1"></i><code class="text-white">gamemode</code>
                                    </button>
                                    <button class="btn btn-sm glass-btn btn-outline-primary" onclick="insertCommand('tp')" data-bs-toggle="tooltip" title="Teleport player">
                                        <i class="fas fa-location-arrow me-1"></i><code class="text-white">tp</code>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card glass border-0 animate__animated animate__fadeInRight">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3 d-flex align-items-center text-white">
                                    <div class="flex-shrink-0">
                                        <div class="bg-info bg-opacity-20 rounded-circle p-2 position-relative">
                                            <i class="fas fa-history text-info"></i>
                                        </div>
                                    </div>
                                    <span class="ms-3">Command History</span>
                                </h6>
                                <div id="commandHistory" class="glass rounded-3 p-3 border border-info border-opacity-25" style="height: 120px; overflow-y: auto; background: rgba(20, 83, 45, 0.4);">
                                    <small class="text-white-50">Command history will appear here...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Commands & Advanced Tools -->
<div class="row mt-4 g-4">
    <div class="col-md-6">
        <div class="card glass-intense animate__animated animate__fadeInLeft animate__delay-6s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-accent-glass"></i>Quick Commands
                    <span class="badge bg-info ms-2" style="font-size: 0.7rem;">INSTANT</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="executeQuickCommand('list')">
                            <i class="fas fa-users fs-4 mb-2 text-primary"></i>
                            <small class="fw-medium">List Players</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="executeQuickCommand('time set day')">
                            <i class="fas fa-sun fs-4 mb-2 text-warning"></i>
                            <small class="fw-medium">Set Day</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="executeQuickCommand('time set night')">
                            <i class="fas fa-moon fs-4 mb-2 text-secondary"></i>
                            <small class="fw-medium">Set Night</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="executeQuickCommand('weather clear')">
                            <i class="fas fa-cloud-sun fs-4 mb-2 text-success"></i>
                            <small class="fw-medium">Clear Weather</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="executeQuickCommand('weather rain')">
                            <i class="fas fa-cloud-rain fs-4 mb-2 text-info"></i>
                            <small class="fw-medium">Make Rain</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="confirmStopServer()">
                            <i class="fas fa-stop fs-4 mb-2 text-danger"></i>
                            <small class="fw-medium">Stop Server</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card glass-intense animate__animated animate__fadeInRight animate__delay-6s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2 text-accent-glass"></i>Console Tools
                    <span class="badge bg-secondary ms-2" style="font-size: 0.7rem;">ADVANCED</span>
                </h5>
            </div>
            <div class="card-body">
                <!-- Console Controls -->
                <div class="row g-3 mb-4">
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-primary w-100" onclick="exportLogs()">
                            <i class="fas fa-file-export me-2"></i>
                            <small>Export Logs</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-info w-100" onclick="searchLogs()">
                            <i class="fas fa-search me-2"></i>
                            <small>Search Logs</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-warning w-100" onclick="filterLogs()">
                            <i class="fas fa-filter me-2"></i>
                            <small>Filter Logs</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn glass-btn btn-outline-success w-100" onclick="toggleWordWrap()">
                            <i class="fas fa-text-width me-2"></i>
                            <small>Word Wrap</small>
                        </button>
                    </div>
                </div>

                <!-- Console Stats -->
                <div class="card glass border-0 animate__animated animate__fadeInUp">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3 d-flex align-items-center text-white">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-20 rounded-circle p-2 position-relative">
                                    <i class="fas fa-chart-line text-primary"></i>
                                </div>
                            </div>
                            <span class="ms-3">Console Statistics</span>
                        </h6>
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="d-flex align-items-center glass p-3 rounded-3 border border-primary border-opacity-25">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                            <i class="fas fa-terminal text-primary"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1 text-white">Commands</h6>
                                        <span class="fs-4 fw-bold text-primary" id="commandCount">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center glass p-3 rounded-3 border border-success border-opacity-25">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                            <i class="fas fa-sync-alt text-success"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1 text-white">Refreshes</h6>
                                        <span class="fs-4 fw-bold text-success" id="refreshCount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
let autoRefreshEnabled = false;
let autoRefreshInterval;
let commandHistory = [];
let historyIndex = -1;
let commandCount = 0;
let refreshCount = 0;
let isFullscreen = false;

function refreshLogs() {
    const btn = event?.target?.closest('button');
    const icon = btn?.querySelector('i');

    if (icon) {
        icon.classList.add('fa-spin');
    }

    // Update refresh status icon
    const refreshStatusIcon = document.getElementById('refreshStatusIcon');
    if (refreshStatusIcon) {
        refreshStatusIcon.classList.add('fa-spin');
    }

    fetch('/api/console?lines=100')
        .then(response => response.json())
        .then(data => {
            if (data.logs) {
                const consoleContent = document.querySelector('.console-content');
                consoleContent.textContent = data.logs;

                // Scroll to bottom with smooth animation
                const consoleOutput = document.getElementById('consoleOutput');
                consoleOutput.scrollTo({
                    top: consoleOutput.scrollHeight,
                    behavior: 'smooth'
                });

                // Update timestamps and counters
                const now = new Date();
                const timeString = now.toLocaleTimeString();

                // Update multiple timestamp elements
                const lastUpdatedElements = ['lastUpdated', 'lastUpdatedBadge'];
                lastUpdatedElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = timeString;
                    }
                });

                // Update refresh counter
                refreshCount++;
                const refreshCountElement = document.getElementById('refreshCount');
                if (refreshCountElement) {
                    refreshCountElement.textContent = refreshCount;
                }

                ToastManager.show('Console logs refreshed!', 'success', 2000);
            }
        })
        .catch(error => {
            console.error('Error refreshing logs:', error);
            ToastManager.show('Failed to refresh logs', 'error');
        })
        .finally(() => {
            if (icon) {
                icon.classList.remove('fa-spin');
            }
            if (refreshStatusIcon) {
                refreshStatusIcon.classList.remove('fa-spin');
            }
        });
}

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    const icon = document.getElementById('autoRefreshIcon');
    const status = document.getElementById('autoRefreshStatus');
    const badge = document.getElementById('autoRefreshBadge');

    if (autoRefreshEnabled) {
        icon.className = 'fas fa-pause';
        if (status) {
            status.textContent = 'Enabled';
            status.classList.add('text-success');
            status.classList.remove('text-muted');
        }
        if (badge) {
            badge.innerHTML = '<i class="fas fa-play me-1"></i>ENABLED';
            badge.className = 'badge bg-success';
        }
        autoRefreshInterval = setInterval(refreshLogs, 5000);
        ToastManager.show('Auto-refresh enabled (every 5 seconds)', 'info');
    } else {
        icon.className = 'fas fa-play';
        if (status) {
            status.textContent = 'Disabled';
            status.classList.add('text-muted');
            status.classList.remove('text-success');
        }
        if (badge) {
            badge.innerHTML = '<i class="fas fa-pause me-1"></i>DISABLED';
            badge.className = 'badge bg-secondary';
        }
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
        ToastManager.show('Auto-refresh disabled', 'info');
    }
}

function toggleFullscreen() {
    const consoleOutput = document.getElementById('consoleOutput');
    const icon = document.getElementById('fullscreenIcon');

    if (!isFullscreen) {
        consoleOutput.style.position = 'fixed';
        consoleOutput.style.top = '0';
        consoleOutput.style.left = '0';
        consoleOutput.style.width = '100vw';
        consoleOutput.style.height = '100vh';
        consoleOutput.style.zIndex = '9999';
        consoleOutput.style.borderRadius = '0';
        icon.className = 'fas fa-compress';
        isFullscreen = true;
        ToastManager.show('Console in fullscreen mode', 'info');
    } else {
        consoleOutput.style.position = 'relative';
        consoleOutput.style.top = 'auto';
        consoleOutput.style.left = 'auto';
        consoleOutput.style.width = 'auto';
        consoleOutput.style.height = '500px';
        consoleOutput.style.zIndex = 'auto';
        consoleOutput.style.borderRadius = '12px';
        icon.className = 'fas fa-expand';
        isFullscreen = false;
        ToastManager.show('Console in normal mode', 'info');
    }
}

function confirmStopServer() {
    Swal.fire({
        title: '⚠️ Stop Server?',
        text: 'This will stop the Minecraft server. All players will be disconnected!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, stop server!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            executeQuickCommand('stop');
        }
    });
}

function exportLogs() {
    ToastManager.show('Preparing log export...', 'info');
    downloadLogs();
}

function searchLogs() {
    Swal.fire({
        title: 'Search Console Logs',
        input: 'text',
        inputPlaceholder: 'Enter search term...',
        showCancelButton: true,
        confirmButtonText: 'Search',
        inputValidator: (value) => {
            if (!value) {
                return 'Please enter a search term!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const searchTerm = result.value;
            const consoleContent = document.querySelector('.console-content');
            const text = consoleContent.textContent;
            const lines = text.split('\n');
            const matches = lines.filter(line => line.toLowerCase().includes(searchTerm.toLowerCase()));

            if (matches.length > 0) {
                ToastManager.show(`Found ${matches.length} matches for "${searchTerm}"`, 'success');
                // Highlight matches (basic implementation)
                const highlightedText = text.replace(
                    new RegExp(searchTerm, 'gi'),
                    `<mark style="background: rgba(245, 158, 11, 0.3); color: #facc15;">$&</mark>`
                );
                consoleContent.innerHTML = highlightedText;
            } else {
                ToastManager.show(`No matches found for "${searchTerm}"`, 'warning');
            }
        }
    });
}

function filterLogs() {
    Swal.fire({
        title: 'Filter Console Logs',
        input: 'select',
        inputOptions: {
            'all': 'Show All',
            'info': 'Info Messages',
            'warn': 'Warnings',
            'error': 'Errors',
            'player': 'Player Activity',
            'server': 'Server Events'
        },
        inputPlaceholder: 'Select filter type',
        showCancelButton: true,
        confirmButtonText: 'Apply Filter'
    }).then((result) => {
        if (result.isConfirmed) {
            const filterType = result.value;
            ToastManager.show(`Applied filter: ${filterType}`, 'info');
            // Basic filter implementation
            if (filterType === 'all') {
                refreshLogs();
            } else {
                ToastManager.show('Advanced filtering coming soon!', 'info');
            }
        }
    });
}

function toggleWordWrap() {
    const consoleContent = document.querySelector('.console-content');
    const currentWrap = consoleContent.style.whiteSpace;

    if (currentWrap === 'pre-wrap') {
        consoleContent.style.whiteSpace = 'pre';
        consoleContent.style.overflowX = 'auto';
        ToastManager.show('Word wrap disabled', 'info');
    } else {
        consoleContent.style.whiteSpace = 'pre-wrap';
        consoleContent.style.overflowX = 'hidden';
        ToastManager.show('Word wrap enabled', 'info');
    }
}

function clearConsole() {
    Swal.fire({
        title: 'Clear Console?',
        text: 'This will clear the current console view (logs will still be available on refresh)',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6366f1',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, clear it!'
    }).then((result) => {
        if (result.isConfirmed) {
            const consoleContent = document.querySelector('.console-content');
            consoleContent.textContent = 'Console cleared...\n';
            ToastManager.show('Console cleared!', 'success');
        }
    });
}

function downloadLogs() {
    fetch('/api/console?lines=1000')
        .then(response => response.json())
        .then(data => {
            if (data.logs) {
                const blob = new Blob([data.logs], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `minecraft-server-logs-${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                ToastManager.show('Logs downloaded successfully!', 'success');
            }
        })
        .catch(error => {
            ToastManager.show('Failed to download logs', 'error');
        });
}

function insertCommand(command) {
    const input = document.getElementById('commandInput');
    input.value = command;
    input.focus();

    // Add visual feedback
    input.classList.add('animate__animated', 'animate__pulse');
    setTimeout(() => {
        input.classList.remove('animate__animated', 'animate__pulse');
    }, 1000);
}

function executeQuickCommand(command) {
    const input = document.getElementById('commandInput');
    input.value = command;

    // Add visual feedback to the button
    const btn = event.target.closest('button');
    btn.classList.add('animate__animated', 'animate__pulse');
    setTimeout(() => {
        btn.classList.remove('animate__animated', 'animate__pulse');
    }, 1000);

    // Execute the command
    document.getElementById('commandForm').dispatchEvent(new Event('submit'));
}

function addToHistory(command) {
    if (command && !commandHistory.includes(command)) {
        commandHistory.unshift(command);
        if (commandHistory.length > 10) {
            commandHistory.pop();
        }
        updateHistoryDisplay();
    }
    historyIndex = -1;
}

function updateHistoryDisplay() {
    const historyDiv = document.getElementById('commandHistory');
    if (commandHistory.length === 0) {
        historyDiv.innerHTML = '<small class="text-white-50">Command history will appear here...</small>';
    } else {
        historyDiv.innerHTML = commandHistory.map((cmd, index) =>
            `<div class="mb-2 animate__animated animate__fadeIn" style="animation-delay: ${index * 0.1}s;">
                <button class="btn btn-sm btn-outline-primary w-100 text-start d-flex align-items-center" onclick="insertCommand('${cmd}')" data-bs-toggle="tooltip" title="Click to use this command">
                    <i class="fas fa-history me-2 text-primary"></i>
                    <code class="text-white">/${cmd}</code>
                </button>
            </div>`
        ).join('');

        // Initialize tooltips for new elements
        const tooltipTriggerList = [].slice.call(historyDiv.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

// Enhanced command form handling
document.addEventListener('DOMContentLoaded', function() {
    const commandForm = document.getElementById('commandForm');
    const commandInput = document.getElementById('commandInput');

    // Handle form submission
    commandForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const command = commandInput.value.trim();

        if (!command) return;

        // Add to history
        addToHistory(command);

        // Update command counter
        commandCount++;
        const commandCountElement = document.getElementById('commandCount');
        if (commandCountElement) {
            commandCountElement.textContent = commandCount;
        }

        // Visual feedback
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Executing...';
        submitBtn.disabled = true;

        // Show command in console with enhanced formatting
        const consoleContent = document.querySelector('.console-content');
        const timestamp = new Date().toLocaleTimeString();
        const commandLine = `\n[${timestamp}] [ADMIN] > /${command}\n[Command executed via web console - check server logs for output]\n`;
        consoleContent.textContent += commandLine;

        // Scroll to bottom
        const consoleOutput = document.getElementById('consoleOutput');
        consoleOutput.scrollTo({
            top: consoleOutput.scrollHeight,
            behavior: 'smooth'
        });

        // Clear input and restore button
        setTimeout(() => {
            commandInput.value = '';
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            ToastManager.show(`Command "/${command}" executed successfully!`, 'success');

            // Refresh logs to see output
            setTimeout(refreshLogs, 1000);
        }, 1000);
    });

    // Handle keyboard shortcuts
    commandInput.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (historyIndex < commandHistory.length - 1) {
                historyIndex++;
                this.value = commandHistory[historyIndex];
            }
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (historyIndex > 0) {
                historyIndex--;
                this.value = commandHistory[historyIndex];
            } else if (historyIndex === 0) {
                historyIndex = -1;
                this.value = '';
            }
        }
    });

    // Auto-scroll console to bottom (only if console exists)
    const consoleOutput = document.getElementById('consoleOutput');
    if (consoleOutput) {
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }

    // Initialize history display
    updateHistoryDisplay();
});
</script>
{% endblock %}

{% endblock %}
